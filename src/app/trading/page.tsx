'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import OrderBook from '@/components/trading/OrderBook'
import EnergyMarketplace from '@/components/trading/EnergyMarketplace'
import TradingDashboard from '@/components/trading/TradingDashboard'
import { useWallet } from '@/components/providers/WalletProvider'
import { Activity, TrendingUp, BarChart3, Zap } from 'lucide-react'

export default function TradingPage() {
  const { connected, connect } = useWallet()

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Activity className="h-6 w-6" />
              Energy Trading Platform
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              Connect your MasChain wallet to access advanced trading features
            </p>
            <button
              onClick={connect}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Connect Wallet
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Activity className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Energy Trading</h1>
            <Badge className="bg-green-100 text-green-800">
              Phase 2 - Live Trading
            </Badge>
          </div>
          <p className="text-gray-600">
            Real-time energy trading with advanced order book and matching engine
          </p>
        </div>

        {/* Trading Interface */}
        <Tabs defaultValue="orderbook" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="orderbook" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Order Book
            </TabsTrigger>
            <TabsTrigger value="marketplace" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Marketplace
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="orderbook">
            <OrderBook />
          </TabsContent>

          <TabsContent value="marketplace">
            <EnergyMarketplace />
          </TabsContent>

          <TabsContent value="dashboard">
            <TradingDashboard />
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Trading History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Trading history will be displayed here</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Complete trades to see your transaction history
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Feature Highlights */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-3">
                <BarChart3 className="h-6 w-6 text-blue-600" />
                <h3 className="font-semibold">Real-time Order Book</h3>
              </div>
              <p className="text-sm text-gray-600">
                Live order matching with instant execution and market depth visualization
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-3">
                <Zap className="h-6 w-6 text-green-600" />
                <h3 className="font-semibold">Instant Settlement</h3>
              </div>
              <p className="text-sm text-gray-600">
                Automated trade execution with blockchain settlement in under 3 seconds
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-3">
                <TrendingUp className="h-6 w-6 text-purple-600" />
                <h3 className="font-semibold">Advanced Analytics</h3>
              </div>
              <p className="text-sm text-gray-600">
                Market insights, price forecasting, and performance analytics
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
